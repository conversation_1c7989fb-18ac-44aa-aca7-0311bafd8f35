/* 基础页面样式 */
.page {
	min-height: 100vh;
	background-color: #ffffff;
}

/* 面包屑导航样式 */
.breadcrumb-nav {
	width: 100%;
	max-width: 1025px;
	margin: 13px auto 0;
}

.breadcrumb-container {
	display: flex;
	align-items: center;
	gap: 6px;
	height: 22px;
}

.breadcrumb-item {
	color: rgba(0, 0, 0, 0.6);
	font-size: 14px;
	font-family: PingFangSC-Regular, sans-serif;
	font-weight: normal;
	text-decoration: none;
	white-space: nowrap;
	line-height: 22px;
	transition: color 0.3s ease;
}

.breadcrumb-item:hover {
	color: rgba(0, 85, 195, 1);
	text-decoration: underline;
}

.breadcrumb-item:focus {
	outline: 2px solid rgba(0, 85, 195, 0.5);
	outline-offset: 2px;
	border-radius: 2px;
}

.breadcrumb-current {
	color: rgba(0, 0, 0, 0.6);
	font-size: 14px;
	font-family: PingFangSC-Regular, sans-serif;
	font-weight: normal;
	white-space: nowrap;
	line-height: 22px;
}

.breadcrumb-separator {
	width: 18px;
	height: 18px;
	flex-shrink: 0;
	opacity: 0.6;
}

/* 英雄区域 */
.hero-section {
	background-color: #000000;
	width: 100%;
	margin-top: 12px;
	display: flex;
	flex-direction: column;
	justify-content: flex-end;
	min-height: 200px;
}

.hero-container {
	width: 100%;
	display: flex;
	justify-content: center;
	padding: 37px 0;
}

.hero-content {
	display: flex;
	align-items: center;
	justify-content: space-between;
	gap: 20px;
	max-width: 864px;
	width: 100%;
}

.hero-icon {
	width: 70px;
	height: 69px;
	flex-shrink: 0;
}

.hero-text {
	width: 774px;
	height: 102px;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}

.hero-title {
	font-size: 30px;
	font-family: PingFangSC-Regular, sans-serif;
	font-weight: 400;
	color: #ffffff;
	margin: 0;
	line-height: 42px;
	height: 42px;
}

.hero-description {
	font-size: 18px;
	font-family: PingFangSC-Regular, sans-serif;
	font-weight: 400;
	color: #ffffff;
	line-height: 25px;
	margin: 0;
	height: 50px;
}

.hero-divider {
	background-color: #ff4300;
	height: 4px;
	width: 100%;
	max-width: 1030px;
	margin: 0 auto;
}

/* 联系我们区域 */
.contact-section {
	margin: 40px auto 0;
	max-width: 1030px;
}

.section-header {
	background-color: #0e6be4;
	border-radius: 0 0 25px 25px;
	padding: 9px 60px;
	margin-bottom: 40px;
}

.section-title {
	font-size: 24px;
	font-family: PingFangSC-Regular, sans-serif;
	font-weight: 400;
	color: #ffffff;
	margin: 0;
}

/* 联系表单容器 */
.contact-content {
	width: 100%;
	max-width: 1030px;
	margin: 0 auto;
	display: flex;
	justify-content: center;
	align-items: flex-start;
	border-left: 1px solid #000000;
	border-right: 1px solid #000000;
	border-bottom: 1px solid #000000;
}

.contact-divider {
	display: none;
}

.contact-form-container {
	width: 668px;
	margin: 49px auto 0 auto;
	padding: 0 20px 20px 20px;
}

.contact-divider-right {
	display: none;
}

/* 表单样式 */
.contact-form {
	width: 100%;
}

.form-row {
	display: flex;
	gap: 20px;
	margin-bottom: 20px;
}

.form-group {
	flex: 1;
	margin-bottom: 20px;
}

.form-label {
	display: block;
	font-size: 14px;
	font-family: AlibabaPuHuiTi-Regular, sans-serif;
	font-weight: 400;
	color: #353535;
	margin-bottom: 5px;
}

.required {
	color: #ec2914;
}

.form-input,
.form-select {
	width: 100%;
	height: 34px;
	padding: 6px 12px;
	border: 1px solid #d3d3d3;
	border-radius: 2px;
	font-size: 14px;
	font-family: AlibabaPuHuiTi-Regular, sans-serif;
	font-weight: 400;
	background-color: #ffffff;
	transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-input:focus,
.form-select:focus {
	outline: none;
	border-color: #0e6be4;
	box-shadow: 0 0 0 2px rgba(14, 107, 228, 0.2);
}

.form-input::placeholder {
	color: #d3d3d3;
}

.form-textarea {
	width: 100%;
	min-height: 100px;
	padding: 12px;
	border: 1px solid #d3d3d3;
	border-radius: 2px;
	font-size: 14px;
	font-family: AlibabaPuHuiTi-Regular, sans-serif;
	font-weight: 400;
	background-color: #ffffff;
	resize: vertical;
	transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-textarea:focus {
	outline: none;
	border-color: #0e6be4;
	box-shadow: 0 0 0 2px rgba(14, 107, 228, 0.2);
}

/* 复选框样式 */
.checkbox-group {
	border: 1px solid #000000;
	border-radius: 2px;
	padding: 10px;
	background-color: #ffffff;
}

.checkbox-label {
	display: flex;
	align-items: flex-start;
	gap: 10px;
	cursor: pointer;
}

.form-checkbox {
	width: 16px;
	height: 16px;
	margin-top: 2px;
	flex-shrink: 0;
}

.checkbox-text {
	font-size: 14px;
	font-family: AlibabaPuHuiTi-Regular, sans-serif;
	font-weight: 400;
	color: #000000;
	line-height: 1.4;
	flex: 1;
	word-wrap: break-word;
	word-break: break-all;
	white-space: normal;
}

/* 提交按钮 */
.submit-btn {
	background-color: #ec2914;
	color: #ffffff;
	font-size: 14px;
	font-family: AlibabaPuHuiTi-Regular, sans-serif;
	font-weight: 400;
	padding: 10px 21px;
	border: none;
	border-radius: 2px;
	cursor: pointer;
	margin: 20px auto 0 auto;
	display: block;
	transition: background-color 0.3s ease, transform 0.2s ease;
}

.submit-btn:hover {
	background-color: #d12410;
	transform: translateY(-2px);
}

.submit-btn:active {
	transform: translateY(0);
}

/* 联系信息区域 */
.contact-info-section {
	margin: 0 auto;
	max-width: 1030px;
}

.contact-info-divider {
	width: 100%;
	height: 44px;
	display: block;
}

.contact-info-card {
	box-shadow: 0 0 10px rgba(0, 0, 0, 0.16);
	background-color: #ffffff;
	padding: 26px 80px 30px 80px;
	margin: 40px 0;
}

.contact-info-title {
	font-size: 30px;
	font-family: AlibabaPuHuiTi-Regular, sans-serif;
	font-weight: 400;
	color: #000000;
	margin: 0 0 18px 0;
}

.contact-info-subtitle {
	font-size: 14px;
	font-family: AlibabaPuHuiTi-Medium, sans-serif;
	font-weight: 500;
	color: #000000;
	margin: 16px 0 4px 0;
}

.contact-info-subtitle:first-of-type {
	margin-top: 0;
}

.phone-numbers {
	font-size: 14px;
	font-family: AlibabaPuHuiTi-Regular, sans-serif;
	font-weight: 400;
	color: #000000;
	line-height: 1.4;
	margin: 0;
}

.email-address {
	font-size: 14px;
	font-family: AlibabaPuHuiTi-Regular, sans-serif;
	font-weight: 400;
	color: #fe4400;
	margin: 0;
}

.address {
	font-size: 14px;
	font-family: AlibabaPuHuiTi-Regular, sans-serif;
	font-weight: 400;
	color: #000000;
	margin: 0;
}

/* 产品卡片区域 */
.product-cards-section {
	max-width: 1030px;
	margin: 0 auto;
}

.product-cards-grid {
	display: flex;
	gap: 10px;
}

.product-card {
	flex: 1;
	background-color: #ffffff;
	border: 1px solid #0a0a0a;
	padding: 30px;
	text-align: center;
	cursor: pointer;
	transition: background-color 0.3s ease, color 0.3s ease;
}

.product-card:hover {
	background-color: #000000;
}

.product-card:hover .product-title {
	color: #ffffff;
}

.product-card:hover .product-icon,
.product-card:hover .product-arrow {
	filter: invert(1);
}

/* 产品卡片选中状态 */
.product-card.selected {
	background-color: #000000;
	position: relative;
}

.product-card.selected::after {
	content: "";
	position: absolute;
	bottom: -10px;
	left: 50%;
	transform: translateX(-50%);
	width: 0;
	height: 0;
	border-left: 10px solid transparent;
	border-right: 10px solid transparent;
	border-top: 10px solid #000000;
}

.product-card.selected .product-title {
	color: #ffffff;
}

.product-card.selected .product-icon,
.product-card.selected .product-arrow {
	filter: invert(1);
}

.product-card.featured {
	background-color: #ffffff;
	border: 1px solid #0a0a0a;
}

.product-icon {
	width: 32px;
	height: 32px;
	margin-bottom: 28px;
}

.product-card-content {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 10px;
}

.product-title {
	font-size: 14px;
	font-family: PingFangSC-Regular, sans-serif;
	font-weight: 400;
	color: #000000;
	margin: 0;
}

.product-arrow {
	width: 10px;
	height: 6px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
	.hero-content {
		max-width: 700px;
	}

	.product-cards-section {
		max-width: 900px;
	}

	.breadcrumb-container {
		justify-content: flex-start;
	}
}

@media (max-width: 900px) {
	.hero-container {
		padding: 20px 16px;
	}

	.hero-content {
		max-width: 100%;
		flex-direction: column;
		text-align: center;
		gap: 16px;
	}

	.hero-text {
		width: 100%;
		height: auto;
	}

	.product-cards-section {
		max-width: 100%;
	}

	.contact-content {
		flex-direction: column;
		gap: 16px;
		border: none;
		max-width: 100%;
		margin: 0 16px;
	}

	.contact-form-container {
		width: 100%;
		height: auto;
		margin: 0;
		padding: 16px;
	}

	.contact-divider,
	.contact-divider-right {
		display: none;
	}

	.form-row {
		flex-direction: column;
		gap: 16px;
	}

	.product-cards-grid {
		flex-direction: column;
		gap: 16px;
	}

	.breadcrumb-item,
	.breadcrumb-current {
		font-size: 13px;
	}

	.breadcrumb-separator {
		width: 16px;
		height: 16px;
	}
}

@media (max-width: 600px) {
	.hero-section {
		min-height: 160px;
	}

	.hero-container {
		padding: 16px;
	}

	.hero-title {
		font-size: 24px;
		line-height: 32px;
		height: auto;
	}

	.hero-description {
		font-size: 16px;
		line-height: 22px;
		height: auto;
	}

	.section-title {
		font-size: 20px;
	}

	.contact-info-title {
		font-size: 24px;
	}

	.breadcrumb-container {
		gap: 4px;
		flex-wrap: wrap;
	}

	.breadcrumb-item,
	.breadcrumb-current {
		font-size: 12px;
		line-height: 20px;
	}

	.breadcrumb-separator {
		width: 14px;
		height: 14px;
	}
}
