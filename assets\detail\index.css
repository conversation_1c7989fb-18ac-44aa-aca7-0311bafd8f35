.page {
	position: relative;
	overflow: hidden;
}

.block_1 {
	background-color: rgba(255, 255, 255, 1);
	max-width: 1440px;
	margin: 0 auto;
	display: flex;
	flex-direction: column;
	align-items: center;
}

/* 面包屑导航样式 */
.breadcrumb-nav {
	width: 100%;
	max-width: 1025px;
	margin: 13px auto 0;
}

.breadcrumb-container {
	display: flex;
	align-items: center;
	gap: 6px;
	height: 22px;
}

.breadcrumb-item {
	color: rgba(0, 0, 0, 0.6);
	font-size: 14px;
	font-family: PingFangSC-Regular, sans-serif;
	font-weight: normal;
	text-decoration: none;
	white-space: nowrap;
	line-height: 22px;
	transition: color 0.3s ease;
}

.breadcrumb-item:hover {
	color: rgba(0, 85, 195, 1);
	text-decoration: underline;
}

.breadcrumb-item:focus {
	outline: 2px solid rgba(0, 85, 195, 0.5);
	outline-offset: 2px;
	border-radius: 2px;
}

.breadcrumb-current {
	color: rgba(0, 0, 0, 0.6);
	font-size: 14px;
	font-family: PingFangSC-Regular, sans-serif;
	font-weight: normal;
	white-space: nowrap;
	line-height: 22px;
}

.breadcrumb-separator {
	height: 18px;
	flex-shrink: 0;
	opacity: 0.6;
}

/* 重新设计的区域样式 */
.redesigned-section {
	width: 100%;
	max-width: 1440px;
	margin: 11px auto 0;
	box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.12);
}

/* 上部模块：Banner区域 */
.banner-section {
	background-color: #f2f2f2;
	width: 100%;
	max-width: 1440px;
	height: 382px;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 50px;
	box-sizing: border-box;
	margin: 0 auto;
}

.banner-image {
	max-width: 100%;
	max-height: 100%;
	height: auto;
	width: auto;
	object-fit: contain;
}

/* 下部模块：导航区域 */
.navigation-section {
	background-color: #000000;
	width: 100%;
	max-width: 1440px;
	height: 52px;
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 0 auto;
}

.nav-container {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 80px;
}

.nav-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	position: relative;
	height: 52px;
}

.nav-text {
	color: rgba(255, 255, 255, 1);
	font-size: 16px;
	font-family: PingFangSC-Medium, sans-serif;
	font-weight: 500;
	text-align: center;
	white-space: nowrap;
	line-height: 22px;
	cursor: pointer;
	transition: color 0.3s ease;
	padding: 15px 0;
}

.nav-text:hover {
	color: rgba(255, 255, 255, 1);
}

/* hover时的蓝色条 */
.nav-item::after {
	content: "";
	position: absolute;
	bottom: -3px;
	left: 50%;
	transform: translateX(-50%);
	width: 0;
	height: 3px;
	/* width: 60px; */
	background-color: rgba(0, 85, 195, 1);
	transition: width 0.3s ease;
}

.nav-item:hover::after {
	width: 100%;
}

/* 点击效果 */
.nav-item {
	cursor: pointer;
	transition: all 0.3s ease;
}

.nav-item:active {
	transform: translateY(1px);
}

.nav-item:active .nav-text {
	color: rgba(0, 85, 195, 1);
}

.box_5 {
	background-color: rgba(0, 85, 195, 1);
	width: 72px;
	height: 3px;
	margin: 0 auto;
}

.text_17 {
	width: 871px;
	height: 54px;
	overflow-wrap: break-word;
	color: rgba(0, 0, 0, 1);
	font-size: 20px;
	font-family: AlibabaPuHuiTi-Light;
	font-weight: 300;
	text-align: center;
	line-height: 28px;
	margin: 38px auto 0;
}

.text_18 {
	width: 119px;
	height: 27px;
	overflow-wrap: break-word;
	color: rgba(236, 41, 20, 1);
	font-size: 20px;
	font-family: AlibabaPuHuiTi-Regular;
	font-weight: NaN;
	text-align: center;
	white-space: nowrap;
	line-height: 27px;
	margin: 8px auto 0;
	cursor: pointer;
}

.box_6 {
	box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.12);
	background-color: rgba(255, 255, 255, 1);
	border-radius: 8px;
	width: 100%;
	max-width: 1030px;
	max-height: 838px;
	margin: 42px auto 0;
	padding-bottom: 20px;
}

.text_19 {
	width: 71px;
	height: 50px;
	overflow-wrap: break-word;
	color: rgba(0, 0, 0, 1);
	font-size: 36px;
	font-family: AlibabaPuHuiTi-Regular;
	font-weight: NaN;
	text-align: left;
	white-space: nowrap;
	line-height: 50px;
	margin: 17px 0 0 64px;
}

.text_20 {
	width: 315px;
	height: 24px;
	overflow-wrap: break-word;
	color: rgba(0, 0, 0, 0.9);
	font-size: 18px;
	font-family: AlibabaPuHuiTi-Medium;
	font-weight: 500;
	text-align: left;
	white-space: nowrap;
	line-height: 24px;
	margin: 14px 0 0 64px;
}

.text-wrapper_4 {
	width: 903px;
	height: 120px;
	overflow-wrap: break-word;
	font-size: 0;
	font-family: AlibabaPuHuiTi-Medium;
	font-weight: 500;
	text-align: left;
	line-height: 24px;
	margin: 7px 0 0 64px;
}

.text_21 {
	width: 903px;
	height: 120px;
	overflow-wrap: break-word;
	color: rgba(0, 0, 0, 0.9);
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Medium;
	font-weight: 500;
	text-align: left;
	line-height: 24px;
}

.paragraph_1 {
	width: 903px;
	height: 120px;
	overflow-wrap: break-word;
	color: rgba(0, 0, 0, 0.67);
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Regular;
	font-weight: NaN;
	text-align: left;
	line-height: 24px;
}

.text-wrapper_5 {
	width: 903px;
	height: 120px;
	overflow-wrap: break-word;
	font-size: 0;
	font-family: AlibabaPuHuiTi-Medium;
	font-weight: 500;
	text-align: left;
	line-height: 24px;
	margin: 4px 0 0 64px;
}

.text_22 {
	width: 903px;
	height: 120px;
	overflow-wrap: break-word;
	color: rgba(0, 0, 0, 0.9);
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Medium;
	font-weight: 500;
	text-align: left;
	line-height: 24px;
}

.paragraph_2 {
	width: 903px;
	height: 120px;
	overflow-wrap: break-word;
	color: rgba(0, 0, 0, 0.67);
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Regular;
	font-weight: NaN;
	text-align: left;
	line-height: 24px;
}

.text-wrapper_6 {
	width: 903px;
	height: 120px;
	overflow-wrap: break-word;
	font-size: 0;
	font-family: AlibabaPuHuiTi-Medium;
	font-weight: 500;
	text-align: left;
	line-height: 24px;
	margin: 4px 0 0 64px;
}

.text_23 {
	width: 903px;
	height: 120px;
	overflow-wrap: break-word;
	color: rgba(0, 0, 0, 0.9);
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Medium;
	font-weight: 500;
	text-align: left;
	line-height: 24px;
}

.paragraph_3 {
	width: 903px;
	height: 120px;
	overflow-wrap: break-word;
	color: rgba(0, 0, 0, 0.67);
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Regular;
	font-weight: NaN;
	text-align: left;
	line-height: 24px;
}

.text_24 {
	width: 315px;
	height: 24px;
	overflow-wrap: break-word;
	color: rgba(0, 0, 0, 0.9);
	font-size: 18px;
	font-family: AlibabaPuHuiTi-Medium;
	font-weight: 500;
	text-align: left;
	white-space: nowrap;
	line-height: 24px;
	margin: 11px 0 0 64px;
}

.text-wrapper_7 {
	width: 903px;
	height: 96px;
	overflow-wrap: break-word;
	font-size: 0;
	font-family: AlibabaPuHuiTi-Medium;
	font-weight: 500;
	text-align: left;
	line-height: 24px;
	margin: 7px 0 0 64px;
}

.text_25 {
	width: 903px;
	height: 96px;
	overflow-wrap: break-word;
	color: rgba(0, 0, 0, 0.9);
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Medium;
	font-weight: 500;
	text-align: left;
	line-height: 24px;
}

.paragraph_4 {
	width: 903px;
	height: 96px;
	overflow-wrap: break-word;
	color: rgba(0, 0, 0, 0.67);
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Regular;
	font-weight: NaN;
	text-align: left;
	line-height: 24px;
}

.text-wrapper_8 {
	width: 903px;
	height: 96px;
	overflow-wrap: break-word;
	font-size: 0;
	font-family: AlibabaPuHuiTi-Medium;
	font-weight: 500;
	text-align: left;
	line-height: 24px;
	margin: 4px 0 0 64px;
}

.text_26 {
	width: 903px;
	height: 96px;
	overflow-wrap: break-word;
	color: rgba(0, 0, 0, 0.9);
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Medium;
	font-weight: 500;
	text-align: left;
	line-height: 24px;
}

.paragraph_5 {
	width: 903px;
	height: 96px;
	overflow-wrap: break-word;
	color: rgba(0, 0, 0, 0.67);
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Regular;
	font-weight: NaN;
	text-align: left;
	line-height: 24px;
}

.text-wrapper_9 {
	width: 903px;
	height: 96px;
	overflow-wrap: break-word;
	font-size: 0;
	font-family: AlibabaPuHuiTi-Medium;
	font-weight: 500;
	text-align: left;
	line-height: 24px;
	margin: 4px 0 20px 64px;
}

.text_27 {
	width: 903px;
	height: 96px;
	overflow-wrap: break-word;
	color: rgba(0, 0, 0, 0.9);
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Medium;
	font-weight: 500;
	text-align: left;
	line-height: 24px;
}

.paragraph_6 {
	width: 903px;
	height: 96px;
	overflow-wrap: break-word;
	color: rgba(0, 0, 0, 0.67);
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Regular;
	font-weight: NaN;
	text-align: left;
	line-height: 24px;
}

.image-text_12 {
	width: 1030px;
	height: 50px;
	margin: 57px auto 0;
}

.label_1 {
	width: 30px;
	height: 30px;
	margin-top: 13px;
}

.text-group_1 {
	width: 71px;
	height: 50px;
	overflow-wrap: break-word;
	color: rgba(0, 0, 0, 1);
	font-size: 36px;
	font-family: AlibabaPuHuiTi-Regular;
	font-weight: NaN;
	text-align: left;
	white-space: nowrap;
	line-height: 50px;
}

/* 产品特性区域 - 左图右文布局 */
.product-features {
	box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.16);
	height: 330px;
	width: 100%;
	max-width: 1030px;
	margin: 23px auto 0;
	border-radius: 8px;
	background-color: #ffffff;
	padding: 0;
	box-sizing: border-box;
	display: flex;
	gap: 40px;
	align-items: center;
}

/* 左侧图片区域 */
.features-image {
	flex: 1;
	height: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
}

.feature-main-image {
	width: 100%;
	height: auto;
	max-height: 100%;
	object-fit: cover;
	border-radius: 8px;
}

/* 右侧特性列表区域 */
.features-list {
	flex: 1;
	height: 100%;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	padding: 10px 0;
}

/* 单个特性项 */
.feature-item {
	width: 100%;
	display: flex;
	align-items: center;
	gap: 15px;
	margin-bottom: 8px;
}

.feature-item:last-child {
	margin-bottom: 0;
}

/* 特性项前的圆点 */
.feature-bullet {
	width: 8px;
	height: 8px;
	background-color: #000000;
	border-radius: 50%;
	flex-shrink: 0;
}

/* 特性文字 */
.feature-text {
	color: #000000;
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Regular;
	font-weight: 400;
	line-height: 31px;
	flex: 1;
}

.box_10 {
	box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.12);
	background-color: rgba(255, 255, 255, 1);
	border-radius: 8px;
	width: 100%;
	max-width: 1030px;
	height: 169px;
	margin: 20px auto 0;
}

.text-group_13 {
	width: 930px;
	height: 115px;
	margin: 26px 0 0 50px;
}

.text_28 {
	width: 178px;
	height: 27px;
	overflow-wrap: break-word;
	color: rgba(0, 0, 0, 1);
	font-size: 20px;
	font-family: AlibabaPuHuiTi-Regular;
	font-weight: NaN;
	text-align: left;
	white-space: nowrap;
	line-height: 27px;
}

.text_29 {
	width: 930px;
	height: 72px;
	overflow-wrap: break-word;
	color: rgba(0, 0, 0, 0.67);
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Regular;
	font-weight: NaN;
	text-align: left;
	line-height: 24px;
	margin-top: 16px;
}

.image-text_19 {
	width: 1030px;
	height: 50px;
	margin: 57px auto 0;
}

.label_2 {
	width: 30px;
	height: 30px;
	margin-top: 13px;
}

.text-group_9 {
	width: 71px;
	height: 50px;
	overflow-wrap: break-word;
	color: rgba(0, 0, 0, 1);
	font-size: 36px;
	font-family: AlibabaPuHuiTi-Regular;
	font-weight: NaN;
	text-align: left;
	white-space: nowrap;
	line-height: 50px;
}

.box_11 {
	box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.16);
	height: 194px;
	width: 100%;
	max-width: 1030px;
	margin: 23px auto 60px;
}

.box_12 {
	background-color: rgba(255, 255, 255, 1);
	border-radius: 8px;
	position: relative;
	width: 100%;
	max-width: 1030px;
	height: 194px;
}

.image-text_20 {
	width: 129px;
	height: 74px;
	margin: 61px 0 0 60px;
}

.group_6 {
	background-color: rgba(0, 0, 0, 1);
	border-radius: 50%;
	width: 8px;
	height: 8px;
	margin-top: 54px;
}

.text-group_14 {
	width: 111px;
	height: 74px;
}

.text_30 {
	width: 63px;
	height: 31px;
	overflow-wrap: break-word;
	color: rgba(0, 0, 0, 1);
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Regular;
	font-weight: NaN;
	text-align: left;
	white-space: nowrap;
	line-height: 31px;
}

.text_31 {
	width: 111px;
	height: 31px;
	overflow-wrap: break-word;
	color: rgba(0, 0, 0, 1);
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Regular;
	font-weight: NaN;
	text-align: left;
	white-space: nowrap;
	line-height: 31px;
	margin-top: 12px;
}

.image-text_21 {
	position: absolute;
	left: 60px;
	top: 61px;
	width: 129px;
	height: 74px;
}

.box_13 {
	background-color: rgba(0, 0, 0, 1);
	border-radius: 50%;
	width: 8px;
	height: 8px;
	margin-top: 11px;
}

.text-group_15 {
	width: 111px;
	height: 74px;
}

.text_30 {
	width: 63px;
	height: 31px;
	overflow-wrap: break-word;
	color: rgba(0, 0, 0, 1);
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Regular;
	font-weight: NaN;
	text-align: left;
	white-space: nowrap;
	line-height: 31px;
}

.text_31 {
	width: 111px;
	height: 31px;
	overflow-wrap: break-word;
	color: rgba(0, 0, 0, 1);
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Regular;
	font-weight: NaN;
	text-align: left;
	white-space: nowrap;
	line-height: 31px;
	margin-top: 12px;
}

.box_14 {
	height: 170px;
	background: url(./img/SketchPng1c26c087dcb080c171d06745e5afc6bf9e0a1bbd0f8e73514986c9c3afca5158.png)
		100% no-repeat;
	background-size: 100% 100%;
	width: 100%;
	max-width: 1030px;
	margin: 60px auto 0;
}

.text-wrapper_16 {
	width: 95px;
	height: 33px;
	margin: 26px 0 0 50px;
}

.text_32 {
	width: 95px;
	height: 33px;
	overflow-wrap: break-word;
	color: rgba(0, 0, 0, 1);
	font-size: 24px;
	font-family: AlibabaPuHuiTi-Regular;
	font-weight: NaN;
	text-align: left;
	white-space: nowrap;
	line-height: 33px;
}

.box_19 {
	width: 859px;
	height: 48px;
	margin: 3px 0 60px 50px;
}

.text_33 {
	width: 639px;
	height: 40px;
	overflow-wrap: break-word;
	color: rgba(0, 0, 0, 1);
	font-size: 14px;
	font-family: AlibabaPuHuiTi-Regular;
	font-weight: NaN;
	text-align: left;
	line-height: 20px;
	margin-top: 8px;
}

.text-wrapper_11 {
	background-color: rgba(236, 41, 20, 1);
	height: 46px;
	width: 100px;
}

.text_34 {
	width: 63px;
	height: 22px;
	overflow-wrap: break-word;
	color: rgba(255, 255, 255, 1);
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Regular;
	font-weight: NaN;
	text-align: left;
	white-space: nowrap;
	line-height: 22px;
	margin: 10px 0 0 19px;
}

.box_16 {
	height: 230px;
	background: url(./img/SketchPngc665d90b5754568cc23004f00a8304b254b404ddd99665b5d13240330c29f054.png)
		100% no-repeat;
	background-size: 100% 100%;
	width: 1030px;
	position: relative;
	margin: 60px 0 0 445px;
}

.text-wrapper_17 {
	width: 409px;
	height: 22px;
	margin: 18px 0 0 60px;
}

.text_35 {
	width: 63px;
	height: 22px;
	overflow-wrap: break-word;
	color: rgba(255, 255, 255, 1);
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Bold;
	font-weight: 700;
	text-align: left;
	white-space: nowrap;
	line-height: 22px;
}

.text_36 {
	width: 63px;
	height: 22px;
	overflow-wrap: break-word;
	color: rgba(255, 255, 255, 1);
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Bold;
	font-weight: 700;
	text-align: left;
	white-space: nowrap;
	line-height: 22px;
	margin-left: 110px;
}

.text_37 {
	width: 63px;
	height: 22px;
	overflow-wrap: break-word;
	color: rgba(255, 255, 255, 1);
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Bold;
	font-weight: 700;
	text-align: left;
	white-space: nowrap;
	line-height: 22px;
	margin-left: 110px;
}

.text-wrapper_18 {
	width: 443px;
	height: 22px;
	margin: 12px 0 0 70px;
}

.text_38 {
	width: 63px;
	height: 22px;
	overflow-wrap: break-word;
	color: rgba(255, 255, 255, 1);
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Regular;
	font-weight: NaN;
	text-align: left;
	white-space: nowrap;
	line-height: 22px;
}

.text_39 {
	width: 63px;
	height: 22px;
	overflow-wrap: break-word;
	color: rgba(255, 255, 255, 1);
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Regular;
	font-weight: NaN;
	text-align: left;
	white-space: nowrap;
	line-height: 22px;
	margin-left: 110px;
}

.text_40 {
	width: 95px;
	height: 22px;
	overflow-wrap: break-word;
	color: rgba(255, 255, 255, 1);
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Regular;
	font-weight: NaN;
	text-align: left;
	white-space: nowrap;
	line-height: 22px;
	margin-left: 112px;
}

.text-wrapper_19 {
	width: 63px;
	height: 22px;
	margin: 12px 0 0 70px;
}

.text_41 {
	width: 63px;
	height: 22px;
	overflow-wrap: break-word;
	color: rgba(255, 255, 255, 1);
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Regular;
	font-weight: NaN;
	text-align: left;
	white-space: nowrap;
	line-height: 22px;
}

.text-wrapper_20 {
	width: 32px;
	height: 22px;
	margin: 12px 0 0 70px;
}

.text_42 {
	width: 32px;
	height: 22px;
	overflow-wrap: break-word;
	color: rgba(255, 255, 255, 1);
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Regular;
	font-weight: NaN;
	text-align: left;
	white-space: nowrap;
	line-height: 22px;
}

.block_5 {
	width: 178px;
	height: 55px;
	margin: 30px 0 3px 20px;
}

.image-text_22 {
	width: 178px;
	height: 55px;
}

.image_1 {
	width: 56px;
	height: 55px;
}

.text-group_12 {
	width: 118px;
	height: 42px;
	overflow-wrap: break-word;
	color: rgba(0, 0, 0, 1);
	font-size: 30px;
	font-family: AlibabaPuHuiTi-Medium;
	font-weight: 500;
	text-align: left;
	white-space: nowrap;
	line-height: 42px;
	margin-top: 3px;
}

.block_4 {
	background-color: rgba(0, 0, 0, 1);
	position: absolute;
	left: 234px;
	top: 170px;
	width: 1241px;
	height: 60px;
}

/* 重新设计区域的响应式设计 */
@media (max-width: 1200px) {
	.redesigned-section {
		max-width: 100%;
		margin: 11px 20px 0;
	}

	.banner-section {
		max-width: 100%;
	}

	.navigation-section {
		max-width: 100%;
	}

	.nav-container {
		gap: 60px;
	}
}

@media (max-width: 900px) {
	.banner-section {
		height: 300px;
		padding: 15px;
	}

	.navigation-section {
		height: 52px;
	}

	.nav-item {
		height: 52px;
	}

	.nav-container {
		gap: 40px;
	}

	.nav-text {
		font-size: 15px;
		padding: 15px 0;
	}
}

@media (max-width: 600px) {
	.redesigned-section {
		margin: 11px 10px 0;
	}

	.banner-section {
		height: 250px;
		padding: 10px;
	}

	.navigation-section {
		height: 52px;
	}

	.nav-item {
		height: 52px;
	}

	.nav-container {
		gap: 30px;
	}

	.nav-text {
		font-size: 14px;
		padding: 15px 0;
	}
}

@media (max-width: 480px) {
	.banner-section {
		height: 200px;
	}

	.nav-container {
		gap: 20px;
	}

	.nav-text {
		font-size: 13px;
		padding: 15px 0;
	}
}

/* 面包屑响应式设计 */
@media (max-width: 1200px) {
	.breadcrumb-container {
		justify-content: flex-start;
	}
}

@media (max-width: 900px) {
	.breadcrumb-item,
	.breadcrumb-current {
		font-size: 13px;
	}

	.breadcrumb-separator {
		width: 16px;
		height: 16px;
	}
}

@media (max-width: 600px) {
	.breadcrumb-container {
		gap: 4px;
		flex-wrap: wrap;
	}

	.breadcrumb-item,
	.breadcrumb-current {
		font-size: 12px;
		line-height: 20px;
	}

	.breadcrumb-separator {
		width: 14px;
		height: 14px;
	}

	/* 在小屏幕上隐藏中间的面包屑项，只显示首页和当前页 */
	.breadcrumb-container .breadcrumb-item:nth-child(3),
	.breadcrumb-container .breadcrumb-separator:nth-child(4) {
		display: none;
	}

	.breadcrumb-container .breadcrumb-item:nth-child(1)::after {
		content: " ... ";
		color: rgba(0, 0, 0, 0.4);
		margin: 0 4px;
	}
}
