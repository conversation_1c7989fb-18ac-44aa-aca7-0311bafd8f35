/* 轮播图区域 */
.carousel-container {
	width: 1440px;
	height: 382px;
	margin: 0 auto;
	position: relative;
	overflow: hidden;
}

.carousel-wrapper {
	width: 100%;
	height: 100%;
	position: relative;
}

.carousel-slides {
	width: 100%;
	height: 100%;
	position: relative;
}

.carousel-slide {
	width: 100%;
	height: 100%;
	position: absolute;
	top: 0;
	left: 0;
	background-size: cover;
	background-position: center;
	background-repeat: no-repeat;
	opacity: 0;
	transition: opacity 0.5s ease-in-out;
	display: flex;
	align-items: center;
	justify-content: center;
}

.carousel-slide.active {
	opacity: 1;
}

.slide-content {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	gap: 55px;
	text-align: center;
	z-index: 2;
}

.hero-title {
	font-size: 48px;
	font-family: AlibabaPuHuiTi-Bold;
	font-weight: 700;
	color: #ffffff;
	line-height: 65px;
	margin: 0;
	max-width: 900px;
}

.cta-button {
	background-color: #ec2914;
	color: #ffffff;
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Regular;
	line-height: 22px;
	border: none;
	padding: 12px 19px;
	height: 46px;
	cursor: pointer;
	transition: background-color 0.3s ease;
}

.cta-button:hover {
	background-color: #d12410;
}

/* 指示点样式 */
.carousel-indicators {
	position: absolute;
	bottom: 30px;
	left: 50%;
	transform: translateX(-50%);
	display: flex;
	gap: 20px;
	z-index: 3;
}

.indicator {
	width: 12px;
	height: 12px;
	border-radius: 50%;
	background-color: #ffffff;
	cursor: pointer;
	transition: background-color 0.3s ease;
}

.indicator.active {
	background-color: #ec2914;
}

/* 响应式设计 */
@media (max-width: 1440px) {
	.carousel-container {
		width: 100%;
		max-width: 1440px;
	}
}

@media (max-width: 768px) {
	.carousel-container {
		height: 280px;
	}

	.hero-title {
		font-size: 32px;
		line-height: 44px;
		max-width: 600px;
	}

	.carousel-indicators {
		bottom: 20px;
		gap: 16px;
	}

	.indicator {
		width: 10px;
		height: 10px;
	}
}

.hero-decoration {
	width: 128px;
	height: 14px;
}
