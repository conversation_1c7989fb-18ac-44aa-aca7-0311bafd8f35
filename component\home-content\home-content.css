/* 公司介绍区域 */
.company-intro {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 15px;
	padding-top: 57px;
	text-align: center;
}

.company-title {
	font-size: 36px;
	font-family: AlibabaPuHuiTi-Regular;
	color: #000000;
	line-height: 50px;
	font-weight: 400;
	margin: 0;
}

.company-description {
	font-size: 20px;
	font-family: AlibabaPuHuiTi-Regular;
	color: #000000;
	line-height: 27px;
	margin: 0;
	text-align: right;
	max-width: 910px;
}

/* 解决方案区域 */
.solutions-section {
	max-width: 1030px;
	background-color: #000000;
	height: 120px;
	margin: 56px auto 0;
	display: flex;
	align-items: center;
	justify-content: center;
}

.solutions-content {
	display: flex;
	width: 1030px;
	/* align-items: center; */
}

.solutions-title {
	text-align: center;
	flex: 1;
	font-size: 24px;
	font-family: PingFangSC-Medium;
	font-weight: 500;
	color: #ffffff;
	line-height: 120px;
	margin: 0;
}

.solutions-btn {
	text-align: center;
	flex: 1;
	background-image: url(../../assets/home/<USER>/11c09b6d4d974cfdb0174ba7f2c9e083_mergeImage.png);
	background-size: cover;
	color: #ffffff;
	font-size: 24px;
	font-family: PingFangSC-Medium;
	font-weight: 500;
	border: none;
	line-height: 120px;
	cursor: pointer;
	transition: opacity 0.3s ease;
}

.solutions-btn:hover {
	opacity: 0.8;
}

/* 新闻区域 */
.news-section {
	padding: 60px 0;
	max-width: 1030px;
	margin: 0 auto;
}

.news-header {
	display: flex;
	align-items: end;
	justify-content: space-between;
	margin-bottom: 40px;
	padding-left: 77px;
}

.news-title {
	font-size: 36px;
	font-family: AlibabaPuHuiTi-Regular;
	color: #000000;
	line-height: 50px;
	margin: 0;
	font-weight: 500;
}

.news-nav {
	display: flex;
	align-items: center;
	gap: 10px;
}

.browse-all-link {
	font-size: 14px;
	font-family: AlibabaPuHuiTi-Regular;
	color: #767676;
	text-decoration: none;
	line-height: 20px;
	transition: color 0.3s ease;
}

.browse-all-link.active {
	color: #003a85;
}

.browse-all-link:hover {
	color: #0055c3;
}

.nav-arrow {
	width: 14px;
	height: 14px;
}

.news-divider {
	width: 77px;
	height: 1px;
	margin: 0 20px;
}

.news-grid {
	display: grid;
	grid-template-columns: repeat(4, 1fr);
	gap: 20px;
	max-width: 1160px;
	margin: 0 auto;
}

.news-card {
	background-color: #ffffff;
	overflow: hidden;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
	transition: transform 0.3s ease, box-shadow 0.3s ease;
	display: flex;
	flex-direction: column;
	height: 380px;
}

.news-card:hover {
	transform: translateY(-4px);
	box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.news-image {
	width: 100%;
	height: 180px;
	object-fit: cover;
	object-position: center;
	flex-shrink: 0;
	display: block;
}

.news-content {
	padding: 20px;
	display: flex;
	flex-direction: column;
	gap: 10px;
	flex: 1;
	background-color: #f4f4f4;
}

.news-card-title {
	font-size: 18px;
	font-family: AlibabaPuHuiTi-Regular;
	color: #000000;
	line-height: 24px;
	margin: 0;
	font-weight: 500;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	overflow: hidden;
}

.news-card-summary {
	font-size: 14px;
	font-family: AlibabaPuHuiTi-Light;
	font-weight: 300;
	color: #666666;
	line-height: 20px;
	margin: 0;
	display: -webkit-box;
	-webkit-line-clamp: 3;
	-webkit-box-orient: vertical;
	overflow: hidden;
}

/* 响应式设计 */
@media (max-width: 1200px) {
	.news-grid {
		grid-template-columns: repeat(3, 1fr);
		max-width: 900px;
	}
}

@media (max-width: 900px) {
	.news-grid {
		grid-template-columns: repeat(2, 1fr);
		max-width: 600px;
	}

	.news-header {
		flex-direction: column;
		gap: 20px;
		text-align: center;
	}
}

@media (max-width: 600px) {
	.news-grid {
		grid-template-columns: 1fr;
		max-width: 400px;
	}

	.news-section {
		padding: 40px 0;
	}

	.news-title {
		font-size: 28px;
	}
}
